# uTulz - AI Agent Platform for Advertising Agencies

A modern Next.js application designed specifically for advertising agencies, providing specialized AI tools to automate tasks, generate intelligent content, and optimize campaigns.

## 🚀 Features

- **AI Agent Tools**: Specialized tools for different advertising needs
- **Campaign Management**: Group and integrate multiple tools into campaigns
- **Document Generation**: Generate high-quality files and documents
- **Responsive Design**: Modern, mobile-first design with Tailwind CSS
- **TypeScript**: Full type safety and better developer experience
- **Performance Optimized**: Built with Next.js 15 and modern best practices

## 🛠️ Tech Stack

- **Framework**: Next.js 15 with App Router
- **Language**: TypeScript
- **Styling**: Tailwind CSS v4
- **Fonts**: Inter (Google Fonts)
- **Icons**: Phosphor Icons (SVG)
- **Deployment**: Vercel (recommended)

## 📦 Installation

1. Clone the repository:

```bash
git clone <repository-url>
cd utulz-mvp-3
```

2. Install dependencies:

```bash
npm install
# or
yarn install
# or
pnpm install
```

3. Set up environment variables:

```bash
cp .env.local.example .env.local
# Edit .env.local with your configuration
```

4. Run the development server:

```bash
npm run dev
# or
yarn dev
# or
pnpm dev
```

5. Open [http://localhost:3000](http://localhost:3000) in your browser.

## 🏗️ Project Structure

```
src/
├── app/                 # Next.js App Router pages
│   ├── layout.tsx      # Root layout
│   ├── page.tsx        # Home page
│   └── globals.css     # Global styles
├── components/         # Reusable UI components
│   ├── ui/            # Basic UI components
│   ├── Header.tsx     # Site header
│   ├── Footer.tsx     # Site footer
│   └── ...
└── utils/             # Utility functions and helpers
```

## 🎨 Design System

The application follows a consistent design system:

- **Colors**: Dark theme with blue accents (#1172d4)
- **Typography**: Inter font family
- **Spacing**: Tailwind CSS spacing scale
- **Components**: Modular, reusable components

## 🚀 Deployment

### Vercel (Recommended)

1. Push your code to GitHub
2. Connect your repository to [Vercel](https://vercel.com)
3. Deploy automatically on every push

### Manual Deployment

```bash
npm run build
npm run start
```

## 📝 Environment Variables

Create a `.env.local` file with the following variables:

```env
NEXT_PUBLIC_APP_NAME=uTulz
NEXT_PUBLIC_APP_URL=http://localhost:3000
# Add other environment variables as needed
```

## 🤝 Contributing

1. Fork the repository
2. Create a feature branch: `git checkout -b feature/new-feature`
3. Commit your changes: `git commit -am 'Add new feature'`
4. Push to the branch: `git push origin feature/new-feature`
5. Submit a pull request

## 📄 License

This project is proprietary software. All rights reserved.

## 🆘 Support

For support and questions, please contact the development team or create an issue in the repository.
