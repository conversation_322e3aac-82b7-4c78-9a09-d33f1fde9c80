<html>

<head>
    <link rel="preconnect" href="https://fonts.gstatic.com/" crossorigin="" />
    <link rel="stylesheet" as="style" onload="this.rel='stylesheet'"
        href="https://fonts.googleapis.com/css2?display=swap&amp;family=Inter%3Awght%40400%3B500%3B700%3B900&amp;family=Noto+Sans%3Awght%40400%3B500%3B700%3B900" />

    <title>Stitch Design</title>
    <link rel="icon" type="image/x-icon" href="data:image/x-icon;base64," />

    <script src="https://cdn.tailwindcss.com?plugins=forms,container-queries"></script>
</head>

<body>
    <div class="relative flex size-full min-h-screen flex-col bg-[#111a22] dark group/design-root overflow-x-hidden"
        style='font-family: Inter, "Noto Sans", sans-serif;'>
        <div class="layout-container flex h-full grow flex-col">
            <header
                class="flex items-center justify-between whitespace-nowrap border-b border-solid border-b-[#233648] px-10 py-3">
                <div class="flex items-center gap-4 text-white">
                    <div class="size-4">
                        <svg viewBox="0 0 48 48" fill="none" xmlns="http://www.w3.org/2000/svg">
                            <g clip-path="url(#clip0_6_330)">
                                <path fill-rule="evenodd" clip-rule="evenodd"
                                    d="M24 0.757355L47.2426 24L24 47.2426L0.757355 24L24 0.757355ZM21 35.7574V12.2426L9.24264 24L21 35.7574Z"
                                    fill="currentColor"></path>
                            </g>
                            <defs>
                                <clipPath id="clip0_6_330">
                                    <rect width="48" height="48" fill="white"></rect>
                                </clipPath>
                            </defs>
                        </svg>
                    </div>
                    <h2 class="text-white text-lg font-bold leading-tight tracking-[-0.015em]">uTulz</h2>
                </div>
                <div class="flex flex-1 justify-end gap-8">
                    <div class="flex items-center gap-9">
                        <a class="text-white text-sm font-medium leading-normal" href="#">Recursos</a>
                        <a class="text-white text-sm font-medium leading-normal" href="#">Preços</a>
                        <a class="text-white text-sm font-medium leading-normal" href="#">Suporte</a>
                    </div>
                    <div class="flex gap-2">
                        <button
                            class="flex min-w-[84px] max-w-[480px] cursor-pointer items-center justify-center overflow-hidden rounded-lg h-10 px-4 bg-[#1172d4] text-white text-sm font-bold leading-normal tracking-[0.015em]">
                            <span class="truncate">Criar Conta</span>
                        </button>
                        <button
                            class="flex min-w-[84px] max-w-[480px] cursor-pointer items-center justify-center overflow-hidden rounded-lg h-10 px-4 bg-[#233648] text-white text-sm font-bold leading-normal tracking-[0.015em]">
                            <span class="truncate">Entrar</span>
                        </button>
                    </div>
                </div>
            </header>
            <div class="px-40 flex flex-1 justify-center py-5">
                <div class="layout-content-container flex flex-col max-w-[960px] flex-1">
                    <div class="@container">
                        <div class="@[480px]:p-4">
                            <div class="flex min-h-[480px] flex-col gap-6 bg-cover bg-center bg-no-repeat @[480px]:gap-8 @[480px]:rounded-lg items-center justify-center p-4"
                                style='background-image: linear-gradient(rgba(0, 0, 0, 0.1) 0%, rgba(0, 0, 0, 0.4) 100%), url("https://lh3.googleusercontent.com/aida-public/AB6AXuAGZV4fH_gE-JdmnZFatFs_bPddKomUr33S7O_9L4Ux6TmBJxl0FWsUicHWHuqOa5chLIvdV9oxFTReOuzVAHCyhFygAAPq-aGHp9c5rlINIiFgMumuuDxpCrtYZhB0gyUi0x7pVR6mQgOoqz1wVLpTYmGkcV20pAsrzA7Jg5nXJKeRSP2KGwjgcv_xLV_rfy74ViU62-tYhUTGfb-E0DBD_MdbtieGgWlKYrhLEl6HzTmw9lb-nD-l2mdBKzW6rzqXCBHMRnk4rvM");'>
                                <div class="flex flex-col gap-2 text-center">
                                    <h1
                                        class="text-white text-4xl font-black leading-tight tracking-[-0.033em] @[480px]:text-5xl @[480px]:font-black @[480px]:leading-tight @[480px]:tracking-[-0.033em]">
                                        Potencialize sua Agência com uTulz
                                    </h1>
                                    <h2
                                        class="text-white text-sm font-normal leading-normal @[480px]:text-base @[480px]:font-normal @[480px]:leading-normal">
                                        A plataforma definitiva para agências de publicidade, automatizando tarefas,
                                        gerando conteúdo inteligente e otimizando campanhas.
                                    </h2>
                                </div>
                                <button
                                    class="flex min-w-[84px] max-w-[480px] cursor-pointer items-center justify-center overflow-hidden rounded-lg h-10 px-4 @[480px]:h-12 @[480px]:px-5 bg-[#1172d4] text-white text-sm font-bold leading-normal tracking-[0.015em] @[480px]:text-base @[480px]:font-bold @[480px]:leading-normal @[480px]:tracking-[0.015em]">
                                    <span class="truncate">Criar Conta</span>
                                </button>
                            </div>
                        </div>
                    </div>
                    <h2 class="text-white text-[22px] font-bold leading-tight tracking-[-0.015em] px-4 pb-3 pt-5">
                        Benefícios da uTulz</h2>
                    <div class="flex flex-col gap-10 px-4 py-10 @container">
                        <div class="flex flex-col gap-4">
                            <h1
                                class="text-white tracking-light text-[32px] font-bold leading-tight @[480px]:text-4xl @[480px]:font-black @[480px]:leading-tight @[480px]:tracking-[-0.033em] max-w-[720px]">
                                Transforme sua Agência com uTulz
                            </h1>
                            <p class="text-white text-base font-normal leading-normal max-w-[720px]">
                                Descubra como a uTulz pode revolucionar a forma como sua agência opera, otimizando
                                processos e impulsionando resultados.
                            </p>
                        </div>
                        <div class="grid grid-cols-[repeat(auto-fit,minmax(158px,1fr))] gap-3 p-0">
                            <div class="flex flex-1 gap-3 rounded-lg border border-[#324d67] bg-[#192633] p-4 flex-col">
                                <div class="text-white" data-icon="MagicWand" data-size="24px" data-weight="regular">
                                    <svg xmlns="http://www.w3.org/2000/svg" width="24px" height="24px"
                                        fill="currentColor" viewBox="0 0 256 256">
                                        <path
                                            d="M48,64a8,8,0,0,1,8-8H72V40a8,8,0,0,1,16,0V56h16a8,8,0,0,1,0,16H88V88a8,8,0,0,1-16,0V72H56A8,8,0,0,1,48,64ZM184,192h-8v-8a8,8,0,0,0-16,0v8h-8a8,8,0,0,0,0,16h8v8a8,8,0,0,0,16,0v-8h8a8,8,0,0,0,0-16Zm56-48H224V128a8,8,0,0,0-16,0v16H192a8,8,0,0,0,0,16h16v16a8,8,0,0,0,16,0V160h16a8,8,0,0,0,0-16ZM219.31,80,80,219.31a16,16,0,0,1-22.62,0L36.68,198.63a16,16,0,0,1,0-22.63L176,36.69a16,16,0,0,1,22.63,0l20.68,20.68A16,16,0,0,1,219.31,80Zm-54.63,32L144,91.31l-96,96L68.68,208ZM208,68.69,187.31,48l-32,32L176,100.69Z">
                                        </path>
                                    </svg>
                                </div>
                                <div class="flex flex-col gap-1">
                                    <h2 class="text-white text-base font-bold leading-tight">Automação de Tarefas</h2>
                                    <p class="text-[#92adc9] text-sm font-normal leading-normal">
                                        Automatize tarefas repetitivas e libere sua equipe para focar em estratégias
                                        criativas e de alto impacto.
                                    </p>
                                </div>
                            </div>
                            <div class="flex flex-1 gap-3 rounded-lg border border-[#324d67] bg-[#192633] p-4 flex-col">
                                <div class="text-white" data-icon="Robot" data-size="24px" data-weight="regular">
                                    <svg xmlns="http://www.w3.org/2000/svg" width="24px" height="24px"
                                        fill="currentColor" viewBox="0 0 256 256">
                                        <path
                                            d="M200,48H136V16a8,8,0,0,0-16,0V48H56A32,32,0,0,0,24,80V192a32,32,0,0,0,32,32H200a32,32,0,0,0,32-32V80A32,32,0,0,0,200,48Zm16,144a16,16,0,0,1-16,16H56a16,16,0,0,1-16-16V80A16,16,0,0,1,56,64H200a16,16,0,0,1,16,16Zm-52-56H92a28,28,0,0,0,0,56h72a28,28,0,0,0,0-56Zm-28,16v24H120V152ZM80,164a12,12,0,0,1,12-12h12v24H92A12,12,0,0,1,80,164Zm84,12H152V152h12a12,12,0,0,1,0,24ZM72,108a12,12,0,1,1,12,12A12,12,0,0,1,72,108Zm88,0a12,12,0,1,1,12,12A12,12,0,0,1,160,108Z">
                                        </path>
                                    </svg>
                                </div>
                                <div class="flex flex-col gap-1">
                                    <h2 class="text-white text-base font-bold leading-tight">Geração de Conteúdo
                                        Inteligente</h2>
                                    <p class="text-[#92adc9] text-sm font-normal leading-normal">
                                        Crie conteúdo de alta qualidade de forma rápida e eficiente, utilizando
                                        algoritmos inteligentes e personalizáveis.
                                    </p>
                                </div>
                            </div>
                            <div class="flex flex-1 gap-3 rounded-lg border border-[#324d67] bg-[#192633] p-4 flex-col">
                                <div class="text-white" data-icon="ChartLine" data-size="24px" data-weight="regular">
                                    <svg xmlns="http://www.w3.org/2000/svg" width="24px" height="24px"
                                        fill="currentColor" viewBox="0 0 256 256">
                                        <path
                                            d="M232,208a8,8,0,0,1-8,8H32a8,8,0,0,1-8-8V48a8,8,0,0,1,16,0v94.37L90.73,98a8,8,0,0,1,10.07-.38l58.81,44.11L218.73,90a8,8,0,1,1,10.54,12l-64,56a8,8,0,0,1-10.07.38L96.39,114.29,40,163.63V200H224A8,8,0,0,1,232,208Z">
                                        </path>
                                    </svg>
                                </div>
                                <div class="flex flex-col gap-1">
                                    <h2 class="text-white text-base font-bold leading-tight">Otimização de Campanhas
                                    </h2>
                                    <p class="text-[#92adc9] text-sm font-normal leading-normal">
                                        Maximize o desempenho de suas campanhas com análises detalhadas e ajustes
                                        automáticos para atingir seus objetivos.
                                    </p>
                                </div>
                            </div>
                        </div>
                    </div>
                    <h2 class="text-white text-[22px] font-bold leading-tight tracking-[-0.015em] px-4 pb-3 pt-5">Como
                        Funciona</h2>
                    <div class="grid grid-cols-[40px_1fr] gap-x-2 px-4">
                        <div class="flex flex-col items-center gap-1 pt-5">
                            <div class="size-2 rounded-full bg-white"></div>
                            <div class="w-[1.5px] bg-[#324d67] h-4 grow"></div>
                        </div>
                        <div class="flex flex-1 flex-col py-3">
                            <p class="text-white text-base font-medium leading-normal">Escolha seu Agente</p>
                            <p class="text-[#92adc9] text-base font-normal leading-normal">Selecione o agente virtual
                                mais adequado para suas necessidades específicas.</p>
                        </div>
                        <div class="flex flex-col items-center gap-1">
                            <div class="w-[1.5px] bg-[#324d67] h-4"></div>
                            <div class="size-2 rounded-full bg-white"></div>
                            <div class="w-[1.5px] bg-[#324d67] h-4 grow"></div>
                        </div>
                        <div class="flex flex-1 flex-col py-3">
                            <p class="text-white text-base font-medium leading-normal">Personalize</p>
                            <p class="text-[#92adc9] text-base font-normal leading-normal">Ajuste as configurações e
                                parâmetros para garantir resultados alinhados com seus objetivos.</p>
                        </div>
                        <div class="flex flex-col items-center gap-1 pb-3">
                            <div class="w-[1.5px] bg-[#324d67] h-4"></div>
                            <div class="size-2 rounded-full bg-white"></div>
                        </div>
                        <div class="flex flex-1 flex-col py-3">
                            <p class="text-white text-base font-medium leading-normal">Gere</p>
                            <p class="text-[#92adc9] text-base font-normal leading-normal">Gere documentos e arquivos de
                                alta qualidade com apenas alguns cliques.</p>
                        </div>
                    </div>
                    <h2 class="text-white text-[22px] font-bold leading-tight tracking-[-0.015em] px-4 pb-3 pt-5">Planos
                        e Preços</h2>
                    <div class="grid grid-cols-[repeat(auto-fit,minmax(228px,1fr))] gap-2.5 px-4 py-3 @3xl:grid-cols-4">
                        <div
                            class="flex flex-1 flex-col gap-4 rounded-lg border border-solid border-[#324d67] bg-[#192633] p-6">
                            <div class="flex flex-col gap-1">
                                <h1 class="text-white text-base font-bold leading-tight">Básico</h1>
                                <p class="flex items-baseline gap-1 text-white">
                                    <span class="text-white text-4xl font-black leading-tight tracking-[-0.033em]">R$
                                        99</span>
                                    <span class="text-white text-base font-bold leading-tight">/mês</span>
                                </p>
                            </div>
                            <button
                                class="flex min-w-[84px] max-w-[480px] cursor-pointer items-center justify-center overflow-hidden rounded-lg h-10 px-4 bg-[#233648] text-white text-sm font-bold leading-normal tracking-[0.015em]">
                                <span class="truncate">Assinar</span>
                            </button>
                            <div class="flex flex-col gap-2">
                                <div class="text-[13px] font-normal leading-normal flex gap-3 text-white">
                                    <div class="text-white" data-icon="Check" data-size="20px" data-weight="regular">
                                        <svg xmlns="http://www.w3.org/2000/svg" width="20px" height="20px"
                                            fill="currentColor" viewBox="0 0 256 256">
                                            <path
                                                d="M229.66,77.66l-128,128a8,8,0,0,1-11.32,0l-56-56a8,8,0,0,1,11.32-11.32L96,188.69,218.34,66.34a8,8,0,0,1,11.32,11.32Z">
                                            </path>
                                        </svg>
                                    </div>
                                    Acesso a agentes básicos
                                </div>
                                <div class="text-[13px] font-normal leading-normal flex gap-3 text-white">
                                    <div class="text-white" data-icon="Check" data-size="20px" data-weight="regular">
                                        <svg xmlns="http://www.w3.org/2000/svg" width="20px" height="20px"
                                            fill="currentColor" viewBox="0 0 256 256">
                                            <path
                                                d="M229.66,77.66l-128,128a8,8,0,0,1-11.32,0l-56-56a8,8,0,0,1,11.32-11.32L96,188.69,218.34,66.34a8,8,0,0,1,11.32,11.32Z">
                                            </path>
                                        </svg>
                                    </div>
                                    Geração de até 100 documentos/mês
                                </div>
                                <div class="text-[13px] font-normal leading-normal flex gap-3 text-white">
                                    <div class="text-white" data-icon="Check" data-size="20px" data-weight="regular">
                                        <svg xmlns="http://www.w3.org/2000/svg" width="20px" height="20px"
                                            fill="currentColor" viewBox="0 0 256 256">
                                            <path
                                                d="M229.66,77.66l-128,128a8,8,0,0,1-11.32,0l-56-56a8,8,0,0,1,11.32-11.32L96,188.69,218.34,66.34a8,8,0,0,1,11.32,11.32Z">
                                            </path>
                                        </svg>
                                    </div>
                                    Suporte por e-mail
                                </div>
                            </div>
                        </div>
                        <div
                            class="flex flex-1 flex-col gap-4 rounded-lg border border-solid border-[#324d67] bg-[#192633] p-6">
                            <div class="flex flex-col gap-1">
                                <h1 class="text-white text-base font-bold leading-tight">Profissional</h1>
                                <p class="flex items-baseline gap-1 text-white">
                                    <span class="text-white text-4xl font-black leading-tight tracking-[-0.033em]">R$
                                        199</span>
                                    <span class="text-white text-base font-bold leading-tight">/mês</span>
                                </p>
                            </div>
                            <button
                                class="flex min-w-[84px] max-w-[480px] cursor-pointer items-center justify-center overflow-hidden rounded-lg h-10 px-4 bg-[#233648] text-white text-sm font-bold leading-normal tracking-[0.015em]">
                                <span class="truncate">Assinar</span>
                            </button>
                            <div class="flex flex-col gap-2">
                                <div class="text-[13px] font-normal leading-normal flex gap-3 text-white">
                                    <div class="text-white" data-icon="Check" data-size="20px" data-weight="regular">
                                        <svg xmlns="http://www.w3.org/2000/svg" width="20px" height="20px"
                                            fill="currentColor" viewBox="0 0 256 256">
                                            <path
                                                d="M229.66,77.66l-128,128a8,8,0,0,1-11.32,0l-56-56a8,8,0,0,1,11.32-11.32L96,188.69,218.34,66.34a8,8,0,0,1,11.32,11.32Z">
                                            </path>
                                        </svg>
                                    </div>
                                    Acesso a todos os agentes
                                </div>
                                <div class="text-[13px] font-normal leading-normal flex gap-3 text-white">
                                    <div class="text-white" data-icon="Check" data-size="20px" data-weight="regular">
                                        <svg xmlns="http://www.w3.org/2000/svg" width="20px" height="20px"
                                            fill="currentColor" viewBox="0 0 256 256">
                                            <path
                                                d="M229.66,77.66l-128,128a8,8,0,0,1-11.32,0l-56-56a8,8,0,0,1,11.32-11.32L96,188.69,218.34,66.34a8,8,0,0,1,11.32,11.32Z">
                                            </path>
                                        </svg>
                                    </div>
                                    Geração ilimitada de documentos
                                </div>
                                <div class="text-[13px] font-normal leading-normal flex gap-3 text-white">
                                    <div class="text-white" data-icon="Check" data-size="20px" data-weight="regular">
                                        <svg xmlns="http://www.w3.org/2000/svg" width="20px" height="20px"
                                            fill="currentColor" viewBox="0 0 256 256">
                                            <path
                                                d="M229.66,77.66l-128,128a8,8,0,0,1-11.32,0l-56-56a8,8,0,0,1,11.32-11.32L96,188.69,218.34,66.34a8,8,0,0,1,11.32,11.32Z">
                                            </path>
                                        </svg>
                                    </div>
                                    Suporte prioritário
                                </div>
                                <div class="text-[13px] font-normal leading-normal flex gap-3 text-white">
                                    <div class="text-white" data-icon="Check" data-size="20px" data-weight="regular">
                                        <svg xmlns="http://www.w3.org/2000/svg" width="20px" height="20px"
                                            fill="currentColor" viewBox="0 0 256 256">
                                            <path
                                                d="M229.66,77.66l-128,128a8,8,0,0,1-11.32,0l-56-56a8,8,0,0,1,11.32-11.32L96,188.69,218.34,66.34a8,8,0,0,1,11.32,11.32Z">
                                            </path>
                                        </svg>
                                    </div>
                                    Integrações com outras ferramentas
                                </div>
                            </div>
                        </div>
                        <div
                            class="flex flex-1 flex-col gap-4 rounded-lg border border-solid border-[#324d67] bg-[#192633] p-6">
                            <div class="flex flex-col gap-1">
                                <h1 class="text-white text-base font-bold leading-tight">Empresarial</h1>
                                <p class="flex items-baseline gap-1 text-white">
                                    <span class="text-white text-4xl font-black leading-tight tracking-[-0.033em]">R$
                                        399</span>
                                    <span class="text-white text-base font-bold leading-tight">/mês</span>
                                </p>
                            </div>
                            <button
                                class="flex min-w-[84px] max-w-[480px] cursor-pointer items-center justify-center overflow-hidden rounded-lg h-10 px-4 bg-[#233648] text-white text-sm font-bold leading-normal tracking-[0.015em]">
                                <span class="truncate">Assinar</span>
                            </button>
                            <div class="flex flex-col gap-2">
                                <div class="text-[13px] font-normal leading-normal flex gap-3 text-white">
                                    <div class="text-white" data-icon="Check" data-size="20px" data-weight="regular">
                                        <svg xmlns="http://www.w3.org/2000/svg" width="20px" height="20px"
                                            fill="currentColor" viewBox="0 0 256 256">
                                            <path
                                                d="M229.66,77.66l-128,128a8,8,0,0,1-11.32,0l-56-56a8,8,0,0,1,11.32-11.32L96,188.69,218.34,66.34a8,8,0,0,1,11.32,11.32Z">
                                            </path>
                                        </svg>
                                    </div>
                                    Acesso a todos os agentes
                                </div>
                                <div class="text-[13px] font-normal leading-normal flex gap-3 text-white">
                                    <div class="text-white" data-icon="Check" data-size="20px" data-weight="regular">
                                        <svg xmlns="http://www.w3.org/2000/svg" width="20px" height="20px"
                                            fill="currentColor" viewBox="0 0 256 256">
                                            <path
                                                d="M229.66,77.66l-128,128a8,8,0,0,1-11.32,0l-56-56a8,8,0,0,1,11.32-11.32L96,188.69,218.34,66.34a8,8,0,0,1,11.32,11.32Z">
                                            </path>
                                        </svg>
                                    </div>
                                    Geração ilimitada de documentos
                                </div>
                                <div class="text-[13px] font-normal leading-normal flex gap-3 text-white">
                                    <div class="text-white" data-icon="Check" data-size="20px" data-weight="regular">
                                        <svg xmlns="http://www.w3.org/2000/svg" width="20px" height="20px"
                                            fill="currentColor" viewBox="0 0 256 256">
                                            <path
                                                d="M229.66,77.66l-128,128a8,8,0,0,1-11.32,0l-56-56a8,8,0,0,1,11.32-11.32L96,188.69,218.34,66.34a8,8,0,0,1,11.32,11.32Z">
                                            </path>
                                        </svg>
                                    </div>
                                    Suporte VIP
                                </div>
                                <div class="text-[13px] font-normal leading-normal flex gap-3 text-white">
                                    <div class="text-white" data-icon="Check" data-size="20px" data-weight="regular">
                                        <svg xmlns="http://www.w3.org/2000/svg" width="20px" height="20px"
                                            fill="currentColor" viewBox="0 0 256 256">
                                            <path
                                                d="M229.66,77.66l-128,128a8,8,0,0,1-11.32,0l-56-56a8,8,0,0,1,11.32-11.32L96,188.69,218.34,66.34a8,8,0,0,1,11.32,11.32Z">
                                            </path>
                                        </svg>
                                    </div>
                                    Integrações avançadas
                                </div>
                                <div class="text-[13px] font-normal leading-normal flex gap-3 text-white">
                                    <div class="text-white" data-icon="Check" data-size="20px" data-weight="regular">
                                        <svg xmlns="http://www.w3.org/2000/svg" width="20px" height="20px"
                                            fill="currentColor" viewBox="0 0 256 256">
                                            <path
                                                d="M229.66,77.66l-128,128a8,8,0,0,1-11.32,0l-56-56a8,8,0,0,1,11.32-11.32L96,188.69,218.34,66.34a8,8,0,0,1,11.32,11.32Z">
                                            </path>
                                        </svg>
                                    </div>
                                    Gerente de contas dedicado
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            <footer class="flex justify-center">
                <div class="flex max-w-[960px] flex-1 flex-col">
                    <footer class="flex flex-col gap-6 px-5 py-10 text-center @container">
                        <div
                            class="flex flex-wrap items-center justify-center gap-6 @[480px]:flex-row @[480px]:justify-around">
                            <a class="text-[#92adc9] text-base font-normal leading-normal min-w-40" href="#">Termos de
                                Uso</a>
                            <a class="text-[#92adc9] text-base font-normal leading-normal min-w-40"
                                href="#">Privacidade</a>
                            <a class="text-[#92adc9] text-base font-normal leading-normal min-w-40" href="#">Contato</a>
                        </div>
                        <p class="text-[#92adc9] text-base font-normal leading-normal">© 2023 uTulz. Todos os direitos
                            reservados.</p>
                    </footer>
                </div>
            </footer>
        </div>
    </div>
</body>

</html>