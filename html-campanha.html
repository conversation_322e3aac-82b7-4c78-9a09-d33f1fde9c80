<html>

<head>
    <link rel="preconnect" href="https://fonts.gstatic.com/" crossorigin="" />
    <link rel="stylesheet" as="style" onload="this.rel='stylesheet'"
        href="https://fonts.googleapis.com/css2?display=swap&amp;family=Inter%3Awght%40400%3B500%3B700%3B900&amp;family=Noto+Sans%3Awght%40400%3B500%3B700%3B900" />

    <title>Stitch Design</title>
    <link rel="icon" type="image/x-icon" href="data:image/x-icon;base64," />

    <script src="https://cdn.tailwindcss.com?plugins=forms,container-queries"></script>
</head>

<body>
    <div class="relative flex size-full min-h-screen flex-col bg-[#111a22] dark group/design-root overflow-x-hidden"
        style='font-family: Inter, "Noto Sans", sans-serif;'>
        <div class="layout-container flex h-full grow flex-col">
            <header
                class="flex items-center justify-between whitespace-nowrap border-b border-solid border-b-[#233648] px-10 py-3">
                <div class="flex items-center gap-4 text-white">
                    <div class="size-4">
                        <svg viewBox="0 0 48 48" fill="none" xmlns="http://www.w3.org/2000/svg">
                            <g clip-path="url(#clip0_6_330)">
                                <path fill-rule="evenodd" clip-rule="evenodd"
                                    d="M24 0.757355L47.2426 24L24 47.2426L0.757355 24L24 0.757355ZM21 35.7574V12.2426L9.24264 24L21 35.7574Z"
                                    fill="currentColor"></path>
                            </g>
                            <defs>
                                <clipPath id="clip0_6_330">
                                    <rect width="48" height="48" fill="white"></rect>
                                </clipPath>
                            </defs>
                        </svg>
                    </div>
                    <h2 class="text-white text-lg font-bold leading-tight tracking-[-0.015em]">uTulz</h2>
                </div>
                <div class="flex flex-1 justify-end gap-8">
                    <div class="flex items-center gap-9">
                        <a class="text-white text-sm font-medium leading-normal" href="#">Início</a>
                        <a class="text-white text-sm font-medium leading-normal" href="#">Recursos</a>
                        <a class="text-white text-sm font-medium leading-normal" href="#">Preços</a>
                        <a class="text-white text-sm font-medium leading-normal" href="#">Suporte</a>
                    </div>
                    <div class="flex gap-2">
                        <button
                            class="flex min-w-[84px] max-w-[480px] cursor-pointer items-center justify-center overflow-hidden rounded-lg h-10 px-4 bg-[#1172d4] text-white text-sm font-bold leading-normal tracking-[0.015em]">
                            <span class="truncate">Entrar</span>
                        </button>
                        <button
                            class="flex min-w-[84px] max-w-[480px] cursor-pointer items-center justify-center overflow-hidden rounded-lg h-10 px-4 bg-[#233648] text-white text-sm font-bold leading-normal tracking-[0.015em]">
                            <span class="truncate">Experimente Grátis</span>
                        </button>
                    </div>
                </div>
            </header>
            <div class="px-40 flex flex-1 justify-center py-5">
                <div class="layout-content-container flex flex-col max-w-[960px] flex-1">
                    <div class="@container">
                        <div class="@[480px]:p-4">
                            <div class="flex min-h-[480px] flex-col gap-6 bg-cover bg-center bg-no-repeat @[480px]:gap-8 @[480px]:rounded-lg items-center justify-center p-4"
                                style='background-image: linear-gradient(rgba(0, 0, 0, 0.1) 0%, rgba(0, 0, 0, 0.4) 100%), url("https://lh3.googleusercontent.com/aida-public/AB6AXuBNVG59ovnphrURsvMTsTdKn5uoY2VyX8VuGP_AIqSBPZ0GSyiumQ--UCAinWqvxchrYoydV8KiO8gZfhQRAURaXFOSglsoeFto_aIxlUor9iX68QZAj15ASPKhwMYfXkgnN-bLLAMS-DlsSACiSi5wDw1ZmEwJRYj6f9J0HPka8dw-7_J8LLVZ_56zB8iCaqw_608n1p91upiMzwFn4oYDDAnzGq_9yoFIeJvT4gEDzL5mT_i89caQJeU9GtjN1aBsrYgvnFCI-OA");'>
                                <div class="flex flex-col gap-2 text-center">
                                    <h1
                                        class="text-white text-4xl font-black leading-tight tracking-[-0.033em] @[480px]:text-5xl @[480px]:font-black @[480px]:leading-tight @[480px]:tracking-[-0.033em]">
                                        Crie Campanhas Poderosas com uTulz
                                    </h1>
                                    <h2
                                        class="text-white text-sm font-normal leading-normal @[480px]:text-base @[480px]:font-normal @[480px]:leading-normal">
                                        Transforme suas ideias em campanhas de sucesso com nossa plataforma intuitiva.
                                        Alcance resultados excepcionais com ferramentas avançadas e suporte
                                        especializado.
                                    </h2>
                                </div>
                                <button
                                    class="flex min-w-[84px] max-w-[480px] cursor-pointer items-center justify-center overflow-hidden rounded-lg h-10 px-4 @[480px]:h-12 @[480px]:px-5 bg-[#1172d4] text-white text-sm font-bold leading-normal tracking-[0.015em] @[480px]:text-base @[480px]:font-bold @[480px]:leading-normal @[480px]:tracking-[0.015em]">
                                    <span class="truncate">Criar sua Primeira Campanha</span>
                                </button>
                            </div>
                        </div>
                    </div>
                    <h2 class="text-white text-[22px] font-bold leading-tight tracking-[-0.015em] px-4 pb-3 pt-5">Como
                        Funciona</h2>
                    <div class="grid grid-cols-[40px_1fr] gap-x-2 px-4">
                        <div class="flex flex-col items-center gap-1 pt-3">
                            <div class="text-white" data-icon="TextHThree" data-size="24px" data-weight="regular">
                                <svg xmlns="http://www.w3.org/2000/svg" width="24px" height="24px" fill="currentColor"
                                    viewBox="0 0 256 256">
                                    <path
                                        d="M152,56V176a8,8,0,0,1-16,0V124H48v52a8,8,0,0,1-16,0V56a8,8,0,0,1,16,0v52h88V56a8,8,0,0,1,16,0Zm73.52,90.63,21-30A8,8,0,0,0,240,104H192a8,8,0,0,0,0,16h32.63l-19.18,27.41A8,8,0,0,0,212,160a20,20,0,1,1-14.29,34,8,8,0,1,0-11.42,11.19A36,36,0,0,0,248,180,36.07,36.07,0,0,0,225.52,146.63Z">
                                    </path>
                                </svg>
                            </div>
                            <div class="w-[1.5px] bg-[#324d67] h-2 grow"></div>
                        </div>
                        <div class="flex flex-1 flex-col py-3">
                            <p class="text-white text-base font-medium leading-normal">Selecione seus Agentes</p>
                            <p class="text-[#92adc9] text-base font-normal leading-normal">Escolha os agentes ideais
                                para sua campanha com base em suas habilidades e experiência.</p>
                        </div>
                        <div class="flex flex-col items-center gap-1">
                            <div class="w-[1.5px] bg-[#324d67] h-2"></div>
                            <div class="text-white" data-icon="TextHThree" data-size="24px" data-weight="regular">
                                <svg xmlns="http://www.w3.org/2000/svg" width="24px" height="24px" fill="currentColor"
                                    viewBox="0 0 256 256">
                                    <path
                                        d="M152,56V176a8,8,0,0,1-16,0V124H48v52a8,8,0,0,1-16,0V56a8,8,0,0,1,16,0v52h88V56a8,8,0,0,1,16,0Zm73.52,90.63,21-30A8,8,0,0,0,240,104H192a8,8,0,0,0,0,16h32.63l-19.18,27.41A8,8,0,0,0,212,160a20,20,0,1,1-14.29,34,8,8,0,1,0-11.42,11.19A36,36,0,0,0,248,180,36.07,36.07,0,0,0,225.52,146.63Z">
                                    </path>
                                </svg>
                            </div>
                            <div class="w-[1.5px] bg-[#324d67] h-2 grow"></div>
                        </div>
                        <div class="flex flex-1 flex-col py-3">
                            <p class="text-white text-base font-medium leading-normal">Configure sua Campanha</p>
                            <p class="text-[#92adc9] text-base font-normal leading-normal">Defina os parâmetros da sua
                                campanha, incluindo orçamento, cronograma e metas.</p>
                        </div>
                        <div class="flex flex-col items-center gap-1 pb-3">
                            <div class="w-[1.5px] bg-[#324d67] h-2"></div>
                            <div class="text-white" data-icon="TextHThree" data-size="24px" data-weight="regular">
                                <svg xmlns="http://www.w3.org/2000/svg" width="24px" height="24px" fill="currentColor"
                                    viewBox="0 0 256 256">
                                    <path
                                        d="M152,56V176a8,8,0,0,1-16,0V124H48v52a8,8,0,0,1-16,0V56a8,8,0,0,1,16,0v52h88V56a8,8,0,0,1,16,0Zm73.52,90.63,21-30A8,8,0,0,0,240,104H192a8,8,0,0,0,0,16h32.63l-19.18,27.41A8,8,0,0,0,212,160a20,20,0,1,1-14.29,34,8,8,0,1,0-11.42,11.19A36,36,0,0,0,248,180,36.07,36.07,0,0,0,225.52,146.63Z">
                                    </path>
                                </svg>
                            </div>
                        </div>
                        <div class="flex flex-1 flex-col py-3">
                            <p class="text-white text-base font-medium leading-normal">Execute e Monitore</p>
                            <p class="text-[#92adc9] text-base font-normal leading-normal">Acompanhe o desempenho da sua
                                campanha em tempo real e faça ajustes conforme necessário.</p>
                        </div>
                    </div>
                    <h2 class="text-white text-[22px] font-bold leading-tight tracking-[-0.015em] px-4 pb-3 pt-5">
                        Depoimentos</h2>
                    <div
                        class="flex overflow-y-auto [-ms-scrollbar-style:none] [scrollbar-width:none] [&amp;::-webkit-scrollbar]:hidden">
                        <div class="flex items-stretch p-4 gap-3">
                            <div class="flex h-full flex-1 flex-col gap-4 rounded-lg min-w-60">
                                <div
                                    class="w-full bg-center bg-no-repeat aspect-square bg-cover rounded-lg flex flex-col">
                                </div>
                                <div>
                                    <p class="text-white text-base font-medium leading-normal">
                                        "uTulz revolucionou a forma como criamos campanhas. A plataforma é fácil de usar
                                        e os resultados são incríveis."
                                    </p>
                                    <p class="text-[#92adc9] text-sm font-normal leading-normal">Sofia Almeida, Agência
                                        Digital</p>
                                </div>
                            </div>
                            <div class="flex h-full flex-1 flex-col gap-4 rounded-lg min-w-60">
                                <div
                                    class="w-full bg-center bg-no-repeat aspect-square bg-cover rounded-lg flex flex-col">
                                </div>
                                <div>
                                    <p class="text-white text-base font-medium leading-normal">
                                        "A capacidade de monitorar o desempenho em tempo real nos permite otimizar
                                        nossas campanhas e maximizar o ROI."
                                    </p>
                                    <p class="text-[#92adc9] text-sm font-normal leading-normal">Carlos Mendes, Agência
                                        de Marketing</p>
                                </div>
                            </div>
                            <div class="flex h-full flex-1 flex-col gap-4 rounded-lg min-w-60">
                                <div
                                    class="w-full bg-center bg-no-repeat aspect-square bg-cover rounded-lg flex flex-col">
                                </div>
                                <div>
                                    <p class="text-white text-base font-medium leading-normal">
                                        "O suporte da equipe uTulz é excepcional. Eles estão sempre prontos para ajudar
                                        e garantir o sucesso de nossas campanhas."
                                    </p>
                                    <p class="text-[#92adc9] text-sm font-normal leading-normal">Isabela Costa, Agência
                                        de Publicidade</p>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="@container">
                        <div
                            class="flex flex-col justify-end gap-6 px-4 py-10 @[480px]:gap-8 @[480px]:px-10 @[480px]:py-20">
                            <div class="flex flex-col gap-2 text-center">
                                <h1
                                    class="text-white tracking-light text-[32px] font-bold leading-tight @[480px]:text-4xl @[480px]:font-black @[480px]:leading-tight @[480px]:tracking-[-0.033em] max-w-[720px]">
                                    Comece a Criar Campanhas Poderosas Hoje Mesmo
                                </h1>
                            </div>
                            <div class="flex flex-1 justify-center">
                                <div class="flex justify-center">
                                    <button
                                        class="flex min-w-[84px] max-w-[480px] cursor-pointer items-center justify-center overflow-hidden rounded-lg h-10 px-4 @[480px]:h-12 @[480px]:px-5 bg-[#1172d4] text-white text-sm font-bold leading-normal tracking-[0.015em] @[480px]:text-base @[480px]:font-bold @[480px]:leading-normal @[480px]:tracking-[0.015em] grow">
                                        <span class="truncate">Ver Planos e Preços</span>
                                    </button>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            <footer class="flex justify-center">
                <div class="flex max-w-[960px] flex-1 flex-col">
                    <footer class="flex flex-col gap-6 px-5 py-10 text-center @container">
                        <div
                            class="flex flex-wrap items-center justify-center gap-6 @[480px]:flex-row @[480px]:justify-around">
                            <a class="text-[#92adc9] text-base font-normal leading-normal min-w-40" href="#">Sobre
                                nós</a>
                            <a class="text-[#92adc9] text-base font-normal leading-normal min-w-40" href="#">Contato</a>
                            <a class="text-[#92adc9] text-base font-normal leading-normal min-w-40" href="#">Termos de
                                Serviço</a>
                            <a class="text-[#92adc9] text-base font-normal leading-normal min-w-40" href="#">Política de
                                Privacidade</a>
                        </div>
                        <div class="flex flex-wrap justify-center gap-4">
                            <a href="#">
                                <div class="text-[#92adc9]" data-icon="TwitterLogo" data-size="24px"
                                    data-weight="regular">
                                    <svg xmlns="http://www.w3.org/2000/svg" width="24px" height="24px"
                                        fill="currentColor" viewBox="0 0 256 256">
                                        <path
                                            d="M247.39,68.94A8,8,0,0,0,240,64H209.57A48.66,48.66,0,0,0,168.1,40a46.91,46.91,0,0,0-33.75,13.7A47.9,47.9,0,0,0,120,88v6.09C79.74,83.47,46.81,50.72,46.46,50.37a8,8,0,0,0-13.65,4.92c-4.31,47.79,9.57,79.77,22,98.18a110.93,110.93,0,0,0,21.88,24.2c-15.23,17.53-39.21,26.74-39.47,26.84a8,8,0,0,0-3.85,11.93c.75,1.12,3.75,5.05,11.08,8.72C53.51,229.7,65.48,232,80,232c70.67,0,129.72-54.42,135.75-124.44l29.91-29.9A8,8,0,0,0,247.39,68.94Zm-45,29.41a8,8,0,0,0-2.32,5.14C196,166.58,143.28,216,80,216c-10.56,0-18-1.4-23.22-3.08,11.51-6.25,27.56-17,37.88-32.48A8,8,0,0,0,92,169.08c-.47-.27-43.91-26.34-44-96,16,13,45.25,33.17,78.67,38.79A8,8,0,0,0,136,104V88a32,32,0,0,1,9.6-22.92A30.94,30.94,0,0,1,167.9,56c12.66.16,24.49,7.88,29.44,19.21A8,8,0,0,0,204.67,80h16Z">
                                        </path>
                                    </svg>
                                </div>
                            </a>
                            <a href="#">
                                <div class="text-[#92adc9]" data-icon="FacebookLogo" data-size="24px"
                                    data-weight="regular">
                                    <svg xmlns="http://www.w3.org/2000/svg" width="24px" height="24px"
                                        fill="currentColor" viewBox="0 0 256 256">
                                        <path
                                            d="M128,24A104,104,0,1,0,232,128,104.11,104.11,0,0,0,128,24Zm8,191.63V152h24a8,8,0,0,0,0-16H136V112a16,16,0,0,1,16-16h16a8,8,0,0,0,0-16H152a32,32,0,0,0-32,32v24H96a8,8,0,0,0,0,16h24v63.63a88,88,0,1,1,16,0Z">
                                        </path>
                                    </svg>
                                </div>
                            </a>
                            <a href="#">
                                <div class="text-[#92adc9]" data-icon="InstagramLogo" data-size="24px"
                                    data-weight="regular">
                                    <svg xmlns="http://www.w3.org/2000/svg" width="24px" height="24px"
                                        fill="currentColor" viewBox="0 0 256 256">
                                        <path
                                            d="M128,80a48,48,0,1,0,48,48A48.05,48.05,0,0,0,128,80Zm0,80a32,32,0,1,1,32-32A32,32,0,0,1,128,160ZM176,24H80A56.06,56.06,0,0,0,24,80v96a56.06,56.06,0,0,0,56,56h96a56.06,56.06,0,0,0,56-56V80A56.06,56.06,0,0,0,176,24Zm40,152a40,40,0,0,1-40,40H80a40,40,0,0,1-40-40V80A40,40,0,0,1,80,40h96a40,40,0,0,1,40,40ZM192,76a12,12,0,1,1-12-12A12,12,0,0,1,192,76Z">
                                        </path>
                                    </svg>
                                </div>
                            </a>
                        </div>
                        <p class="text-[#92adc9] text-base font-normal leading-normal">© 2023 uTulz. Todos os direitos
                            reservados.</p>
                    </footer>
                </div>
            </footer>
        </div>
    </div>
</body>

</html>