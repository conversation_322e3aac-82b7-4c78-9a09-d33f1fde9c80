import React from 'react';

interface StepCardProps {
  icon: React.ReactNode;
  title: string;
  description: string;
  isLast?: boolean;
}

const StepCard: React.FC<StepCardProps> = ({
  icon,
  title,
  description,
  isLast = false
}) => {
  return (
    <>
      <div className="flex flex-col items-center gap-1 pt-3">
        <div className="text-white">
          {icon}
        </div>
        {!isLast && <div className="w-[1.5px] bg-[#324d67] h-2 grow"></div>}
      </div>
      <div className="flex flex-1 flex-col py-3">
        <p className="text-white text-base font-medium leading-normal">
          {title}
        </p>
        <p className="text-[#92adc9] text-base font-normal leading-normal">
          {description}
        </p>
      </div>
    </>
  );
};

export default StepCard;
