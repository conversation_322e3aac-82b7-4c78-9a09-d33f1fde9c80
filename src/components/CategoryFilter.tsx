import React from 'react';

interface CategoryFilterProps {
  categories: string[];
  activeCategory: string;
  onCategoryChange: (category: string) => void;
  searchText: string;
  onSearchChange: (text: string) => void;
  selectedCampaign: string;
  onCampaignChange: (campaign: string) => void;
  showMvpOnly: boolean;
  onMvpToggle: (show: boolean) => void;
  campaigns: string[];
}

const CategoryFilter: React.FC<CategoryFilterProps> = ({
  categories,
  activeCategory,
  onCategoryChange,
  searchText,
  onSearchChange,
  selectedCampaign,
  onCampaignChange,
  showMvpOnly,
  onMvpToggle,
  campaigns
}) => {
  return (
    <div className="flex flex-col gap-4 px-4 py-4 border-b border-[#324d67]">
      {/* Search and Filters Row */}
      <div className="flex flex-wrap gap-4 items-center">
        {/* Search Input */}
        <div className="flex-1 min-w-[200px]">
          <input
            type="text"
            placeholder="Buscar ferramentas..."
            value={searchText}
            onChange={(e) => onSearchChange(e.target.value)}
            className="w-full px-4 py-2 bg-[#233648] border border-[#324d67] rounded-lg text-white placeholder-[#92adc9] focus:outline-none focus:border-[#1172d4] transition-colors"
          />
        </div>

        {/* Campaign Filter */}
        <div className="min-w-[150px]">
          <select
            value={selectedCampaign}
            onChange={(e) => onCampaignChange(e.target.value)}
            className="w-full px-4 py-2 bg-[#233648] border border-[#324d67] rounded-lg text-white focus:outline-none focus:border-[#1172d4] transition-colors"
          >
            <option value="">Todas as campanhas</option>
            {campaigns.map((campaign) => (
              <option key={campaign} value={campaign}>
                {campaign}
              </option>
            ))}
          </select>
        </div>

        {/* MVP Only Toggle */}
        <div className="flex items-center gap-2">
          <input
            type="checkbox"
            id="mvp-only"
            checked={showMvpOnly}
            onChange={(e) => onMvpToggle(e.target.checked)}
            className="w-4 h-4 text-[#1172d4] bg-[#233648] border-[#324d67] rounded focus:ring-[#1172d4] focus:ring-2"
          />
          <label htmlFor="mvp-only" className="text-white text-sm font-medium">
            Mostrar só MVP
          </label>
        </div>
      </div>

      {/* Category Buttons */}
      <div className="flex flex-wrap gap-2">
        {categories.map((category) => (
          <button
            key={category}
            onClick={() => onCategoryChange(category)}
            className={`px-4 py-2 rounded-lg text-sm font-medium transition-colors ${activeCategory === category
                ? 'bg-[#1172d4] text-white'
                : 'bg-[#233648] text-[#92adc9] hover:bg-[#324d67] hover:text-white'
              }`}
          >
            {category}
          </button>
        ))}
      </div>
    </div>
  );
};

export default CategoryFilter;
