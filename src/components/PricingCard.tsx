import React from 'react';
import Button from './ui/Button';

interface PricingFeature {
  text: string;
  included: boolean;
}

interface PricingCardProps {
  title: string;
  price: string;
  period: string;
  features: PricingFeature[];
  buttonText?: string;
  highlighted?: boolean;
}

const CheckIcon: React.FC = () => (
  <svg xmlns="http://www.w3.org/2000/svg" width="20px" height="20px" fill="currentColor" viewBox="0 0 256 256">
    <path d="M229.66,77.66l-128,128a8,8,0,0,1-11.32,0l-56-56a8,8,0,0,1,11.32-11.32L96,188.69,218.34,66.34a8,8,0,0,1,11.32,11.32Z" />
  </svg>
);

const PricingCard: React.FC<PricingCardProps> = ({
  title,
  price,
  period,
  features,
  buttonText = "Assinar",
  highlighted = false
}) => {
  return (
    <div className={`flex flex-1 flex-col gap-4 rounded-lg border border-solid p-6 ${
      highlighted 
        ? 'border-[#1172d4] bg-[#192633] ring-2 ring-[#1172d4]/20' 
        : 'border-[#324d67] bg-[#192633]'
    }`}>
      <div className="flex flex-col gap-1">
        <h1 className="text-white text-base font-bold leading-tight">
          {title}
        </h1>
        <p className="flex items-baseline gap-1 text-white">
          <span className="text-white text-4xl font-black leading-tight tracking-[-0.033em]">
            {price}
          </span>
          <span className="text-white text-base font-bold leading-tight">
            {period}
          </span>
        </p>
      </div>
      
      <Button variant={highlighted ? "primary" : "secondary"}>
        {buttonText}
      </Button>
      
      <div className="flex flex-col gap-2">
        {features.map((feature, index) => (
          <div key={index} className="text-[13px] font-normal leading-normal flex gap-3 text-white">
            <div className="text-white">
              <CheckIcon />
            </div>
            {feature.text}
          </div>
        ))}
      </div>
    </div>
  );
};

export default PricingCard;
