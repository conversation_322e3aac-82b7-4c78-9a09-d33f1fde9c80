import React from 'react';
import Button from './ui/Button';

const CampaignHero: React.FC = () => {
  return (
    <div className="@container">
      <div className="@[480px]:p-4">
        <div 
          className="flex min-h-[480px] flex-col gap-6 bg-cover bg-center bg-no-repeat @[480px]:gap-8 @[480px]:rounded-lg items-center justify-center p-4"
          style={{
            backgroundImage: 'linear-gradient(rgba(0, 0, 0, 0.1) 0%, rgba(0, 0, 0, 0.4) 100%), url("https://lh3.googleusercontent.com/aida-public/AB6AXuBNVG59ovnphrURsvMTsTdKn5uoY2VyX8VuGP_AIqSBPZ0GSyiumQ--UCAinWqvxchrYoydV8KiO8gZfhQRAURaXFOSglsoeFto_aIxlUor9iX68QZAj15ASPKhwMYfXkgnN-bLLAMS-DlsSACiSi5wDw1ZmEwJRYj6f9J0HPka8dw-7_J8LLVZ_56zB8iCaqw_608n1p91upiMzwFn4oYDDAnzGq_9yoFIeJvT4gEDzL5mT_i89caQJeU9GtjN1aBsrYgvnFCI-OA")'
          }}
        >
          <div className="flex flex-col gap-2 text-center">
            <h1 className="text-white text-4xl font-black leading-tight tracking-[-0.033em] @[480px]:text-5xl @[480px]:font-black @[480px]:leading-tight @[480px]:tracking-[-0.033em]">
              Crie Campanhas Poderosas com uTulz
            </h1>
            <h2 className="text-white text-sm font-normal leading-normal @[480px]:text-base @[480px]:font-normal @[480px]:leading-normal">
              Transforme suas ideias em campanhas de sucesso com nossa plataforma intuitiva.
              Alcance resultados excepcionais com ferramentas avançadas e suporte especializado.
            </h2>
          </div>
          <Button 
            size="lg"
            className="@[480px]:h-12 @[480px]:px-5 @[480px]:text-base"
          >
            Criar sua Primeira Campanha
          </Button>
        </div>
      </div>
    </div>
  );
};

export default CampaignHero;
