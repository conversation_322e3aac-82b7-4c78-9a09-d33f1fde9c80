import React from 'react';

interface TestimonialCardProps {
  quote: string;
  author: string;
  company: string;
  avatar?: string;
}

const TestimonialCard: React.FC<TestimonialCardProps> = ({
  quote,
  author,
  company,
  avatar
}) => {
  return (
    <div className="flex h-full flex-1 flex-col gap-4 rounded-lg min-w-60">
      <div className="w-full bg-center bg-no-repeat aspect-square bg-cover rounded-lg flex flex-col bg-[#324d67]">
        {avatar && (
          <img 
            src={avatar} 
            alt={`${author} avatar`}
            className="w-full h-full object-cover rounded-lg"
          />
        )}
      </div>
      <div>
        <p className="text-white text-base font-medium leading-normal mb-2">
          "{quote}"
        </p>
        <p className="text-[#92adc9] text-sm font-normal leading-normal">
          {author}, {company}
        </p>
      </div>
    </div>
  );
};

export default TestimonialCard;
