'use client';

import Link from 'next/link';
import React from 'react';
import <PERSON>ton from './ui/Button';

const Header: React.FC = () => {
  return (
    <header className="flex items-center justify-between whitespace-nowrap border-b border-solid border-b-[#233648] px-10 py-3">
      <div className="flex items-center gap-4 text-white">
        <div className="size-4">
          <svg viewBox="0 0 48 48" fill="none" xmlns="http://www.w3.org/2000/svg">
            <g clipPath="url(#clip0_6_330)">
              <path
                fillRule="evenodd"
                clipRule="evenodd"
                d="M24 0.757355L47.2426 24L24 47.2426L0.757355 24L24 0.757355ZM21 35.7574V12.2426L9.24264 24L21 35.7574Z"
                fill="currentColor"
              />
            </g>
            <defs>
              <clipPath id="clip0_6_330">
                <rect width="48" height="48" fill="white" />
              </clipPath>
            </defs>
          </svg>
        </div>
        <h2 className="text-white text-lg font-bold leading-tight tracking-[-0.015em]">
          uTulz
        </h2>
      </div>

      <div className="flex flex-1 justify-end gap-8">
        <nav className="flex items-center gap-9">
          <Link
            href="/"
            className="text-white text-sm font-medium leading-normal hover:text-[#92adc9] transition-colors"
          >
            Início
          </Link>
          <Link
            href="/campanha"
            className="text-white text-sm font-medium leading-normal hover:text-[#92adc9] transition-colors"
          >
            Campanhas
          </Link>
          <Link
            href="#recursos"
            className="text-white text-sm font-medium leading-normal hover:text-[#92adc9] transition-colors"
          >
            Recursos
          </Link>
          <Link
            href="#precos"
            className="text-white text-sm font-medium leading-normal hover:text-[#92adc9] transition-colors"
          >
            Preços
          </Link>
          <Link
            href="#suporte"
            className="text-white text-sm font-medium leading-normal hover:text-[#92adc9] transition-colors"
          >
            Suporte
          </Link>
        </nav>

        <div className="flex gap-2">
          <Button variant="primary">
            Criar Conta
          </Button>
          <Button variant="secondary">
            Entrar
          </Button>
        </div>
      </div>
    </header>
  );
};

export default Header;
