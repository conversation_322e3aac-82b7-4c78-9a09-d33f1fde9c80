import React from 'react';
import Button from './ui/Button';

interface ToolCardProps {
  icon: React.ReactNode;
  title: string;
  description: string;
  category: string;
  features: string[];
  isPopular?: boolean;
}

const ToolCard: React.FC<ToolCardProps> = ({
  icon,
  title,
  description,
  category,
  features,
  isPopular = false
}) => {
  return (
    <div className={`flex flex-1 flex-col gap-4 rounded-lg border border-solid p-6 relative ${
      isPopular 
        ? 'border-[#1172d4] bg-[#192633] ring-2 ring-[#1172d4]/20' 
        : 'border-[#324d67] bg-[#192633]'
    }`}>
      {isPopular && (
        <div className="absolute -top-3 left-4 bg-[#1172d4] text-white text-xs font-bold px-3 py-1 rounded-full">
          Popular
        </div>
      )}
      
      <div className="flex items-center gap-3">
        <div className="text-white">
          {icon}
        </div>
        <div className="flex flex-col">
          <h3 className="text-white text-lg font-bold leading-tight">
            {title}
          </h3>
          <span className="text-[#1172d4] text-sm font-medium">
            {category}
          </span>
        </div>
      </div>
      
      <p className="text-[#92adc9] text-sm font-normal leading-normal">
        {description}
      </p>
      
      <div className="flex flex-col gap-2">
        <h4 className="text-white text-sm font-semibold">Principais recursos:</h4>
        <ul className="space-y-1">
          {features.map((feature, index) => (
            <li key={index} className="text-[#92adc9] text-xs flex items-center gap-2">
              <div className="w-1 h-1 bg-[#1172d4] rounded-full"></div>
              {feature}
            </li>
          ))}
        </ul>
      </div>
      
      <Button 
        variant={isPopular ? "primary" : "secondary"}
        size="sm"
        className="mt-auto"
      >
        Usar Ferramenta
      </Button>
    </div>
  );
};

export default ToolCard;
