
import { ChartLineIcon, MagicWandIcon, RobotIcon } from '@/components/Icons';
import HomeScreen from './(app)/home/<USER>';

export default function Home() {
  const features = [
    {
      icon: <MagicWandIcon size={24} />,
      title: "Automação de Tarefas",
      description: "Automatize tarefas repetitivas e libere sua equipe para focar em estratégias criativas e de alto impacto."
    },
    {
      icon: <RobotIcon size={24} />,
      title: "Geração de Conteúdo Inteligente",
      description: "Crie conteúdo de alta qualidade de forma rápida e eficiente, utilizando algoritmos inteligentes e personalizáveis."
    },
    {
      icon: <ChartLineIcon size={24} />,
      title: "Otimização de Campanhas",
      description: "Maximize o desempenho de suas campanhas com análises detalhadas e ajustes automáticos para atingir seus objetivos."
    }
  ];

  const pricingPlans = [
    {
      title: "Básico",
      price: "R$ 99",
      period: "/mês",
      features: [
        { text: "Acesso a agentes básicos", included: true },
        { text: "Geração de até 100 documentos/mês", included: true },
        { text: "Suporte por e-mail", included: true }
      ]
    },
    {
      title: "Profissional",
      price: "R$ 199",
      period: "/mês",
      features: [
        { text: "Acesso a todos os agentes", included: true },
        { text: "Geração ilimitada de documentos", included: true },
        { text: "Suporte prioritário", included: true },
        { text: "Integrações com outras ferramentas", included: true }
      ],
      highlighted: true
    },
    {
      title: "Empresarial",
      price: "R$ 399",
      period: "/mês",
      features: [
        { text: "Acesso a todos os agentes", included: true },
        { text: "Geração ilimitada de documentos", included: true },
        { text: "Suporte VIP", included: true },
        { text: "Integrações avançadas", included: true },
        { text: "Gerente de contas dedicado", included: true }
      ]
    }
  ];

  return (<><HomeScreen /></>);

}
