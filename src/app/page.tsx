import FeatureCard from '@/components/FeatureCard';
import Footer from '@/components/Footer';
import Header from '@/components/Header';
import Hero from '@/components/Hero';
import { ChartLineIcon, MagicWandIcon, RobotIcon } from '@/components/Icons';
import PricingCard from '@/components/PricingCard';

export default function Home() {
  const features = [
    {
      icon: <MagicWandIcon size={24} />,
      title: "Automação de Tarefas",
      description: "Automatize tarefas repetitivas e libere sua equipe para focar em estratégias criativas e de alto impacto."
    },
    {
      icon: <RobotIcon size={24} />,
      title: "Geração de Conteúdo Inteligente",
      description: "Crie conteúdo de alta qualidade de forma rápida e eficiente, utilizando algoritmos inteligentes e personalizáveis."
    },
    {
      icon: <ChartLineIcon size={24} />,
      title: "Otimização de Campanhas",
      description: "Maximize o desempenho de suas campanhas com análises detalhadas e ajustes automáticos para atingir seus objetivos."
    }
  ];

  const pricingPlans = [
    {
      title: "Básico",
      price: "R$ 99",
      period: "/mês",
      features: [
        { text: "Acesso a agentes básicos", included: true },
        { text: "Geração de até 100 documentos/mês", included: true },
        { text: "Suporte por e-mail", included: true }
      ]
    },
    {
      title: "Profissional",
      price: "R$ 199",
      period: "/mês",
      features: [
        { text: "Acesso a todos os agentes", included: true },
        { text: "Geração ilimitada de documentos", included: true },
        { text: "Suporte prioritário", included: true },
        { text: "Integrações com outras ferramentas", included: true }
      ],
      highlighted: true
    },
    {
      title: "Empresarial",
      price: "R$ 399",
      period: "/mês",
      features: [
        { text: "Acesso a todos os agentes", included: true },
        { text: "Geração ilimitada de documentos", included: true },
        { text: "Suporte VIP", included: true },
        { text: "Integrações avançadas", included: true },
        { text: "Gerente de contas dedicado", included: true }
      ]
    }
  ];

  return (
    <div className="relative flex size-full min-h-screen flex-col bg-[#111a22] dark group/design-root overflow-x-hidden" style={{ fontFamily: 'Inter, "Noto Sans", sans-serif' }}>
      <div className="layout-container flex h-full grow flex-col">
        <Header />

        <div className="px-40 flex flex-1 justify-center py-5">
          <div className="layout-content-container flex flex-col max-w-[960px] flex-1">
            {/* Hero Section */}
            <Hero />

            {/* Benefits Section */}
            <h2 className="text-white text-[22px] font-bold leading-tight tracking-[-0.015em] px-4 pb-3 pt-5">
              Benefícios da uTulz
            </h2>
            <div className="flex flex-col gap-10 px-4 py-10 @container">
              <div className="flex flex-col gap-4">
                <h1 className="text-white tracking-light text-[32px] font-bold leading-tight @[480px]:text-4xl @[480px]:font-black @[480px]:leading-tight @[480px]:tracking-[-0.033em] max-w-[720px]">
                  Transforme sua Agência com uTulz
                </h1>
                <p className="text-white text-base font-normal leading-normal max-w-[720px]">
                  Descubra como a uTulz pode revolucionar a forma como sua agência opera, otimizando processos e impulsionando resultados.
                </p>
              </div>
              <div className="grid grid-cols-[repeat(auto-fit,minmax(158px,1fr))] gap-3 p-0">
                {features.map((feature, index) => (
                  <FeatureCard
                    key={index}
                    icon={feature.icon}
                    title={feature.title}
                    description={feature.description}
                  />
                ))}
              </div>
            </div>

            {/* How It Works Section */}
            <h2 className="text-white text-[22px] font-bold leading-tight tracking-[-0.015em] px-4 pb-3 pt-5">
              Como Funciona
            </h2>
            <div className="grid grid-cols-[40px_1fr] gap-x-2 px-4">
              <div className="flex flex-col items-center gap-1 pt-5">
                <div className="size-2 rounded-full bg-white"></div>
                <div className="w-[1.5px] bg-[#324d67] h-4 grow"></div>
              </div>
              <div className="flex flex-1 flex-col py-3">
                <p className="text-white text-base font-medium leading-normal">Escolha seu Agente</p>
                <p className="text-[#92adc9] text-base font-normal leading-normal">
                  Selecione o agente virtual mais adequado para suas necessidades específicas.
                </p>
              </div>
              <div className="flex flex-col items-center gap-1">
                <div className="w-[1.5px] bg-[#324d67] h-4"></div>
                <div className="size-2 rounded-full bg-white"></div>
                <div className="w-[1.5px] bg-[#324d67] h-4 grow"></div>
              </div>
              <div className="flex flex-1 flex-col py-3">
                <p className="text-white text-base font-medium leading-normal">Personalize</p>
                <p className="text-[#92adc9] text-base font-normal leading-normal">
                  Ajuste as configurações e parâmetros para garantir resultados alinhados com seus objetivos.
                </p>
              </div>
              <div className="flex flex-col items-center gap-1 pb-3">
                <div className="w-[1.5px] bg-[#324d67] h-4"></div>
                <div className="size-2 rounded-full bg-white"></div>
              </div>
              <div className="flex flex-1 flex-col py-3">
                <p className="text-white text-base font-medium leading-normal">Gere</p>
                <p className="text-[#92adc9] text-base font-normal leading-normal">
                  Gere documentos e arquivos de alta qualidade com apenas alguns cliques.
                </p>
              </div>
            </div>

            {/* Pricing Section */}
            <h2 className="text-white text-[22px] font-bold leading-tight tracking-[-0.015em] px-4 pb-3 pt-5">
              Planos e Preços
            </h2>
            <div className="grid grid-cols-[repeat(auto-fit,minmax(228px,1fr))] gap-2.5 px-4 py-3 @3xl:grid-cols-4">
              {pricingPlans.map((plan, index) => (
                <PricingCard
                  key={index}
                  title={plan.title}
                  price={plan.price}
                  period={plan.period}
                  features={plan.features}
                  highlighted={plan.highlighted}
                />
              ))}
            </div>
          </div>
        </div>

        <Footer />
      </div>
    </div>
  );
}
