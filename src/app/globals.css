/* Import Google Fonts - must be first */
@import url('https://fonts.googleapis.com/css2?family=Inter:wght@400;500;700;900&family=Noto+Sans:wght@400;500;700;900&display=swap');

@import "tailwindcss";

:root {
  /* uTulz Color Palette */
  --background: #111a22;
  --foreground: #ffffff;
  --primary: #1172d4;
  --primary-hover: #0f5fb3;
  --secondary: #233648;
  --secondary-hover: #2a4155;
  --border: #324d67;
  --card-bg: #192633;
  --text-muted: #92adc9;

  /* Font Variables */
  --font-inter: var(--font-inter);
}

@theme inline {
  --color-background: var(--background);
  --color-foreground: var(--foreground);
  --color-primary: var(--primary);
  --color-secondary: var(--secondary);
  --color-border: var(--border);
  --font-sans: var(--font-inter);
}

* {
  box-sizing: border-box;
  padding: 0;
  margin: 0;
}

html,
body {
  max-width: 100vw;
  overflow-x: hidden;
  background: var(--background);
  color: var(--foreground);
  font-family: 'Inter', 'Noto Sans', sans-serif;
}

/* Smooth scrolling */
html {
  scroll-behavior: smooth;
}

/* Custom scrollbar for webkit browsers */
::-webkit-scrollbar {
  width: 8px;
}

::-webkit-scrollbar-track {
  background: var(--background);
}

::-webkit-scrollbar-thumb {
  background: var(--border);
  border-radius: 4px;
}

::-webkit-scrollbar-thumb:hover {
  background: var(--text-muted);
}

/* Focus styles for accessibility */
*:focus {
  outline: 2px solid var(--primary);
  outline-offset: 2px;
}

/* Container queries support */
@container (min-width: 480px) {
  .responsive-text {
    font-size: 1.125rem;
  }
}