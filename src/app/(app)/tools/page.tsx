'use client';

import React, { useState } from 'react';
import ToolCard from '@/components/ToolCard';
import CategoryFilter from '@/components/CategoryFilter';
import { 
  PencilIcon, 
  ImageIcon, 
  VideoIcon, 
  BrainIcon, 
  MegaphoneIcon, 
  ChartLineIcon,
  RobotIcon,
  MagicWandIcon 
} from '@/components/Icons';

export default function ToolsScreen() {
  const [activeCategory, setActiveCategory] = useState('Todas');

  const categories = [
    'Todas',
    'Geração de Conteúdo',
    'Design Criativo',
    'Análise de Dados',
    'Marketing Digital',
    'Automação'
  ];

  const tools = [
    {
      id: 1,
      icon: <PencilIcon size={32} />,
      title: 'Gerador de Textos',
      description: 'Crie textos persuasivos e envolventes para suas campanhas publicitárias com IA avançada.',
      category: 'Geração de Conteúdo',
      features: [
        'Textos para anúncios',
        'Descrições de produtos',
        'Posts para redes sociais',
        'E-mails marketing'
      ],
      isPopular: true
    },
    {
      id: 2,
      icon: <ImageIcon size={32} />,
      title: 'Editor de Imagens IA',
      description: 'Edite e otimize imagens automaticamente para diferentes plataformas e formatos.',
      category: 'Design Criativo',
      features: [
        'Redimensionamento automático',
        'Remoção de fundo',
        'Filtros inteligentes',
        'Otimização para web'
      ]
    },
    {
      id: 3,
      icon: <VideoIcon size={32} />,
      title: 'Criador de Vídeos',
      description: 'Produza vídeos profissionais para suas campanhas com templates e IA.',
      category: 'Design Criativo',
      features: [
        'Templates profissionais',
        'Edição automática',
        'Legendas automáticas',
        'Múltiplos formatos'
      ]
    },
    {
      id: 4,
      icon: <ChartLineIcon size={32} />,
      title: 'Análise de Performance',
      description: 'Monitore e analise o desempenho de suas campanhas em tempo real.',
      category: 'Análise de Dados',
      features: [
        'Métricas em tempo real',
        'Relatórios automáticos',
        'Insights de IA',
        'Comparação de campanhas'
      ],
      isPopular: true
    },
    {
      id: 5,
      icon: <MegaphoneIcon size={32} />,
      title: 'Otimizador de Anúncios',
      description: 'Otimize automaticamente seus anúncios para maximizar conversões e ROI.',
      category: 'Marketing Digital',
      features: [
        'Otimização automática',
        'A/B testing',
        'Segmentação inteligente',
        'Bid management'
      ]
    },
    {
      id: 6,
      icon: <BrainIcon size={32} />,
      title: 'Assistente de Estratégia',
      description: 'Receba sugestões estratégicas baseadas em dados para suas campanhas.',
      category: 'Análise de Dados',
      features: [
        'Análise de mercado',
        'Sugestões de público',
        'Previsões de performance',
        'Recomendações de budget'
      ]
    },
    {
      id: 7,
      icon: <RobotIcon size={32} />,
      title: 'Chatbot Inteligente',
      description: 'Automatize o atendimento ao cliente com chatbots personalizados.',
      category: 'Automação',
      features: [
        'Respostas automáticas',
        'Integração com CRM',
        'Múltiplos canais',
        'Aprendizado contínuo'
      ]
    },
    {
      id: 8,
      icon: <MagicWandIcon size={32} />,
      title: 'Gerador de Ideias',
      description: 'Gere ideias criativas e inovadoras para suas campanhas publicitárias.',
      category: 'Geração de Conteúdo',
      features: [
        'Brainstorming automático',
        'Tendências do mercado',
        'Análise de concorrentes',
        'Sugestões personalizadas'
      ]
    }
  ];

  const filteredTools = activeCategory === 'Todas' 
    ? tools 
    : tools.filter(tool => tool.category === activeCategory);

  return (
    <div className="layout-content-container flex flex-col max-w-[960px] flex-1">
      {/* Header Section */}
      <div className="flex flex-col gap-4 px-4 py-10 @container">
        <div className="flex flex-col gap-2 text-center">
          <h1 className="text-white tracking-light text-[32px] font-bold leading-tight @[480px]:text-4xl @[480px]:font-black @[480px]:leading-tight @[480px]:tracking-[-0.033em] max-w-[720px]">
            Ferramentas de IA para Agências
          </h1>
          <p className="text-white text-base font-normal leading-normal max-w-[720px]">
            Descubra nossa coleção completa de ferramentas alimentadas por IA, 
            projetadas especificamente para potencializar suas campanhas publicitárias.
          </p>
        </div>
      </div>

      {/* Category Filter */}
      <CategoryFilter 
        categories={categories}
        activeCategory={activeCategory}
        onCategoryChange={setActiveCategory}
      />

      {/* Tools Grid */}
      <div className="grid grid-cols-1 @[480px]:grid-cols-2 @[960px]:grid-cols-3 gap-4 px-4 py-6">
        {filteredTools.map((tool) => (
          <ToolCard
            key={tool.id}
            icon={tool.icon}
            title={tool.title}
            description={tool.description}
            category={tool.category}
            features={tool.features}
            isPopular={tool.isPopular}
          />
        ))}
      </div>

      {/* CTA Section */}
      <div className="@container">
        <div className="flex flex-col justify-end gap-6 px-4 py-10 @[480px]:gap-8 @[480px]:px-10 @[480px]:py-20">
          <div className="flex flex-col gap-2 text-center">
            <h2 className="text-white tracking-light text-[28px] font-bold leading-tight @[480px]:text-3xl @[480px]:font-black @[480px]:leading-tight @[480px]:tracking-[-0.033em] max-w-[720px]">
              Pronto para Revolucionar suas Campanhas?
            </h2>
            <p className="text-[#92adc9] text-base font-normal leading-normal max-w-[720px]">
              Comece a usar nossas ferramentas hoje mesmo e veja a diferença que a IA pode fazer.
            </p>
          </div>
        </div>
      </div>
    </div>
  );
}
