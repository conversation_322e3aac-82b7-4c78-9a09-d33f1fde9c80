import type { Metada<PERSON> } from "next";
import { Inter } from "next/font/google";
import "./globals.css";

const inter = Inter({
  subsets: ["latin"],
  variable: "--font-inter",
  display: "swap",
});

export const metadata: Metadata = {
  title: "uTulz - Potencialize sua Agência com IA",
  description: "A plataforma definitiva para agências de publicidade, automatizando tarefas, gerando conteúdo inteligente e otimizando campanhas.",
  keywords: ["agência", "publicidade", "IA", "automação", "marketing", "campanhas"],
  authors: [{ name: "uTulz" }],
  creator: "uTulz",
  publisher: "uTulz",
  openGraph: {
    title: "uTulz - Potencialize sua Agência com IA",
    description: "A plataforma definitiva para agências de publicidade, automatizando tarefas, gerando conteúdo inteligente e otimizando campanhas.",
    type: "website",
    locale: "pt_BR",
  },
  twitter: {
    card: "summary_large_image",
    title: "uTulz - Potencialize sua Agência com IA",
    description: "A plataforma definitiva para agências de publicidade, automatizando tarefas, gerando conteúdo inteligente e otimizando campanhas.",
  },
};

export default function RootLayout({
  children,
}: Readonly<{
  children: React.ReactNode;
}>) {
  return (
    <html lang="pt-BR">
      <head>
        <link rel="preconnect" href="https://fonts.gstatic.com" crossOrigin="" />
      </head>
      <body
        className={`${inter.variable} font-sans antialiased`}
        style={{ fontFamily: 'Inter, "Noto Sans", sans-serif' }}
      >
        {children}
      </body>
    </html>
  );
}
