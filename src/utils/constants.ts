// Application constants for the uTulz platform

export const APP_CONFIG = {
  name: 'uTulz',
  description: 'A plataforma definitiva para agências de publicidade',
  version: '1.0.0',
  author: 'uTulz Team',
  url: process.env.NEXT_PUBLIC_APP_URL || 'http://localhost:3000',
} as const;

export const ROUTES = {
  home: '/',
  about: '/sobre',
  features: '/recursos',
  pricing: '/precos',
  contact: '/contato',
  login: '/entrar',
  signup: '/criar-conta',
  dashboard: '/dashboard',
  agents: '/agentes',
  campaigns: '/campanhas',
  documents: '/documentos',
  settings: '/configuracoes',
  support: '/suporte',
  terms: '/termos',
  privacy: '/privacidade',
} as const;

export const PRICING_PLANS = {
  basic: {
    id: 'basic',
    name: 'Básico',
    price: 99,
    currency: 'BRL',
    interval: 'month' as const,
    features: [
      { text: 'Acesso a agentes básicos', included: true },
      { text: 'Geração de até 100 documentos/mês', included: true },
      { text: 'Suporte por e-mail', included: true },
    ],
    limits: {
      documentsPerMonth: 100,
      agentsAccess: 'basic' as const,
      supportLevel: 'email' as const,
      integrations: false,
      advancedFeatures: false,
      dedicatedManager: false,
    },
  },
  professional: {
    id: 'professional',
    name: 'Profissional',
    price: 199,
    currency: 'BRL',
    interval: 'month' as const,
    isPopular: true,
    features: [
      { text: 'Acesso a todos os agentes', included: true },
      { text: 'Geração ilimitada de documentos', included: true },
      { text: 'Suporte prioritário', included: true },
      { text: 'Integrações com outras ferramentas', included: true },
    ],
    limits: {
      documentsPerMonth: 'unlimited' as const,
      agentsAccess: 'all' as const,
      supportLevel: 'priority' as const,
      integrations: true,
      advancedFeatures: true,
      dedicatedManager: false,
    },
  },
  enterprise: {
    id: 'enterprise',
    name: 'Empresarial',
    price: 399,
    currency: 'BRL',
    interval: 'month' as const,
    features: [
      { text: 'Acesso a todos os agentes', included: true },
      { text: 'Geração ilimitada de documentos', included: true },
      { text: 'Suporte VIP', included: true },
      { text: 'Integrações avançadas', included: true },
      { text: 'Gerente de contas dedicado', included: true },
    ],
    limits: {
      documentsPerMonth: 'unlimited' as const,
      agentsAccess: 'all' as const,
      supportLevel: 'vip' as const,
      integrations: true,
      advancedFeatures: true,
      dedicatedManager: true,
    },
  },
} as const;

export const AGENT_CATEGORIES = {
  'content-generation': {
    name: 'Geração de Conteúdo',
    description: 'Agentes especializados em criar conteúdo textual e visual',
    icon: 'RobotIcon',
  },
  'campaign-optimization': {
    name: 'Otimização de Campanhas',
    description: 'Agentes para análise e otimização de campanhas publicitárias',
    icon: 'ChartLineIcon',
  },
  'data-analysis': {
    name: 'Análise de Dados',
    description: 'Agentes para processamento e análise de dados de marketing',
    icon: 'ChartLineIcon',
  },
  'creative-design': {
    name: 'Design Criativo',
    description: 'Agentes para criação e edição de materiais visuais',
    icon: 'MagicWandIcon',
  },
  'social-media': {
    name: 'Redes Sociais',
    description: 'Agentes especializados em gestão de redes sociais',
    icon: 'RobotIcon',
  },
  'email-marketing': {
    name: 'Email Marketing',
    description: 'Agentes para criação e otimização de campanhas de email',
    icon: 'RobotIcon',
  },
  'seo-optimization': {
    name: 'Otimização SEO',
    description: 'Agentes para otimização de conteúdo para mecanismos de busca',
    icon: 'ChartLineIcon',
  },
} as const;

export const DOCUMENT_TYPES = {
  text: { name: 'Texto', extension: '.txt', mimeType: 'text/plain' },
  image: { name: 'Imagem', extension: '.jpg', mimeType: 'image/jpeg' },
  video: { name: 'Vídeo', extension: '.mp4', mimeType: 'video/mp4' },
  audio: { name: 'Áudio', extension: '.mp3', mimeType: 'audio/mpeg' },
  pdf: { name: 'PDF', extension: '.pdf', mimeType: 'application/pdf' },
  presentation: { name: 'Apresentação', extension: '.pptx', mimeType: 'application/vnd.openxmlformats-officedocument.presentationml.presentation' },
  spreadsheet: { name: 'Planilha', extension: '.xlsx', mimeType: 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet' },
} as const;

export const API_ENDPOINTS = {
  auth: {
    login: '/api/auth/login',
    logout: '/api/auth/logout',
    signup: '/api/auth/signup',
    refresh: '/api/auth/refresh',
    profile: '/api/auth/profile',
  },
  agents: {
    list: '/api/agents',
    get: (id: string) => `/api/agents/${id}`,
    create: '/api/agents',
    update: (id: string) => `/api/agents/${id}`,
    delete: (id: string) => `/api/agents/${id}`,
  },
  campaigns: {
    list: '/api/campaigns',
    get: (id: string) => `/api/campaigns/${id}`,
    create: '/api/campaigns',
    update: (id: string) => `/api/campaigns/${id}`,
    delete: (id: string) => `/api/campaigns/${id}`,
  },
  documents: {
    list: '/api/documents',
    get: (id: string) => `/api/documents/${id}`,
    create: '/api/documents',
    update: (id: string) => `/api/documents/${id}`,
    delete: (id: string) => `/api/documents/${id}`,
    download: (id: string) => `/api/documents/${id}/download`,
  },
} as const;

export const VALIDATION_RULES = {
  email: /^[^\s@]+@[^\s@]+\.[^\s@]+$/,
  password: {
    minLength: 8,
    requireUppercase: true,
    requireLowercase: true,
    requireNumbers: true,
    requireSpecialChars: false,
  },
  name: {
    minLength: 2,
    maxLength: 50,
  },
  company: {
    minLength: 2,
    maxLength: 100,
  },
} as const;
